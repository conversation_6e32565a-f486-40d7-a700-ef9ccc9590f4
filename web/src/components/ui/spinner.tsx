'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6', 
  lg: 'w-8 h-8',
  xl: 'w-12 h-12'
};

export const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  className
}) => {
  return (
    <div className="flex justify-center text-center">
      <svg 
        className={cn(sizeClasses[size], 'animate-spin', className)} 
        viewBox="0 0 24 24" 
        fill="currentColor" 
        xmlns="http://www.w3.org/2000/svg"
        role="status"
        aria-label="Loading"
      >
        <path
          d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z"
          opacity="0.25"
        />
        <path
          d="M10.14,1.16a11,11,0,0,0-9,8.92A1.59,1.59,0,0,0,2.46,12,1.52,1.52,0,0,0,4.11,10.7a8,8,0,0,1,6.66-6.61A1.42,1.42,0,0,0,12,2.69h0A1.57,1.57,0,0,0,10.14,1.16Z"
        />
        <span className="sr-only">Loading...</span>
      </svg>
    </div>
  );
};

// Dots spinner variant
export const DotsSpinner: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('flex space-x-1', className)}>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
    </div>
  );
};

// Pulse spinner variant  
export const PulseSpinner: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className="flex justify-center text-center">
      <div className={cn('w-6 h-6 border-2 border-current rounded-full animate-pulse', className)}>
        <span className="sr-only">Loading...</span>
      </div>
    </div>
  );
};

// Bars spinner variant
export const BarsSpinner: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('flex space-x-1', className)}>
      <div className="w-1 h-4 bg-current animate-pulse [animation-delay:-0.4s]"></div>
      <div className="w-1 h-4 bg-current animate-pulse [animation-delay:-0.2s]"></div>
      <div className="w-1 h-4 bg-current animate-pulse"></div>
      <div className="w-1 h-4 bg-current animate-pulse [animation-delay:-0.2s]"></div>
      <div className="w-1 h-4 bg-current animate-pulse [animation-delay:-0.4s]"></div>
    </div>
  );
};

// Loading overlay component
interface LoadingOverlayProps {
  show: boolean;
  message?: string;
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  show,
  message = 'Loading...',
  className
}) => {
  if (!show) return null;

  return (
    <div className={cn(
      'absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50',
      className
    )}>
      <div className="flex flex-col items-center gap-3">
        <Spinner size="lg" />
        {message && (
          <p className="text-sm text-gray-600 dark:text-gray-400">{message}</p>
        )}
      </div>
    </div>
  );
};
