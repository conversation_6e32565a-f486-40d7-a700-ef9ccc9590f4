'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import { toast } from 'sonner';
import { Spinner } from '@/components/ui/spinner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SensitiveInput } from '@/components/ui/sensitive-input';
import { Tooltip } from '@/components/ui/tooltip';

import { getRAGConfig, updateRAGConfig } from '@/lib/apis/retrieval';

interface WebSearchProps {
  saveHandler?: () => void;
}

export default function WebSearch({ saveHandler }: WebSearchProps) {
  const { token } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [webConfig, setWebConfig] = useState<any>(null);

  const webSearchEngines = [
    'searxng', 'yacy', 'google_pse', 'brave', 'kagi', 'mojeek', 'bocha',
    'serpstack', 'serper', 'serply', 'searchapi', 'serpapi', 'duckduckgo',
    'tavily', 'jina', 'bing', 'exa', 'perplexity', 'sougou', 'firecrawl', 'external'
  ];

  const webLoaderEngines = ['playwright', 'firecrawl', 'tavily', 'external'];

  useEffect(() => {
    if (token) {
      loadConfig();
    }
  }, [token]);

  const loadConfig = async () => {
    if (!token) return;

    setIsLoading(true);
    try {
      const res = await getRAGConfig(token);
      if (res) {
        setWebConfig(res.web || {});
        
        // Convert array back to comma-separated string for display
        if (res.web?.WEB_SEARCH_DOMAIN_FILTER_LIST && Array.isArray(res.web.WEB_SEARCH_DOMAIN_FILTER_LIST)) {
          setWebConfig(prev => ({
            ...prev,
            WEB_SEARCH_DOMAIN_FILTER_LIST: res.web.WEB_SEARCH_DOMAIN_FILTER_LIST.join(',')
          }));
        }

        if (res.web?.YOUTUBE_LOADER_LANGUAGE && Array.isArray(res.web.YOUTUBE_LOADER_LANGUAGE)) {
          setWebConfig(prev => ({
            ...prev,
            YOUTUBE_LOADER_LANGUAGE: res.web.YOUTUBE_LOADER_LANGUAGE.join(',')
          }));
        }

        // Convert empty string to "default" for display
        if (res.web?.WEB_LOADER_ENGINE === '' || !res.web?.WEB_LOADER_ENGINE) {
          setWebConfig(prev => ({
            ...prev,
            WEB_LOADER_ENGINE: 'default'
          }));
        }
      }
    } catch (error) {
      console.error('Failed to load web search config:', error);
      toast.error('Failed to load web search settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!token || !webConfig) return;

    setIsSaving(true);
    try {
      const configToSave = { ...webConfig };

      // Convert domain filter string to array before sending
      if (configToSave.WEB_SEARCH_DOMAIN_FILTER_LIST) {
        configToSave.WEB_SEARCH_DOMAIN_FILTER_LIST = configToSave.WEB_SEARCH_DOMAIN_FILTER_LIST.split(',')
          .map((domain: string) => domain.trim())
          .filter((domain: string) => domain.length > 0);
      } else {
        configToSave.WEB_SEARCH_DOMAIN_FILTER_LIST = [];
      }

      // Convert Youtube loader language string to array before sending
      if (configToSave.YOUTUBE_LOADER_LANGUAGE) {
        configToSave.YOUTUBE_LOADER_LANGUAGE = configToSave.YOUTUBE_LOADER_LANGUAGE.split(',')
          .map((lang: string) => lang.trim())
          .filter((lang: string) => lang.length > 0);
      } else {
        configToSave.YOUTUBE_LOADER_LANGUAGE = [];
      }

      // Convert "default" back to empty string for API compatibility
      if (configToSave.WEB_LOADER_ENGINE === 'default') {
        configToSave.WEB_LOADER_ENGINE = '';
      }

      await updateRAGConfig(token, { web: configToSave });

      // Convert back to display format
      setWebConfig(prev => ({
        ...prev,
        WEB_SEARCH_DOMAIN_FILTER_LIST: Array.isArray(configToSave.WEB_SEARCH_DOMAIN_FILTER_LIST) 
          ? configToSave.WEB_SEARCH_DOMAIN_FILTER_LIST.join(',') 
          : configToSave.WEB_SEARCH_DOMAIN_FILTER_LIST,
        YOUTUBE_LOADER_LANGUAGE: Array.isArray(configToSave.YOUTUBE_LOADER_LANGUAGE)
          ? configToSave.YOUTUBE_LOADER_LANGUAGE.join(',')
          : configToSave.YOUTUBE_LOADER_LANGUAGE
      }));

      toast.success('Web search settings saved successfully!');
      if (saveHandler) {
        saveHandler();
      }
    } catch (error) {
      console.error('Failed to save web search settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const updateConfigValue = (key: string, value: any) => {
    setWebConfig((prev: any) => ({
      ...prev,
      [key]: value
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!webConfig) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Failed to load web search configuration
        </p>
      </div>
    );
  }

  return (
    <form
      className="flex flex-col h-full justify-between space-y-3 text-sm"
      onSubmit={(e) => {
        e.preventDefault();
        handleSave();
      }}
    >
      <div className="space-y-6 overflow-y-auto scrollbar-hidden h-full pr-1.5">
        {/* General Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>General</CardTitle>
            <CardDescription>
              Configure web search engine and basic settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Web Search Toggle */}
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Web Search</Label>
              <Switch
                checked={webConfig.ENABLE_WEB_SEARCH || false}
                onCheckedChange={(checked) => updateConfigValue('ENABLE_WEB_SEARCH', checked)}
              />
            </div>

            {/* Search Engine Selection */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Web Search Engine</Label>
              <Select
                value={webConfig.WEB_SEARCH_ENGINE || ''}
                onValueChange={(value) => updateConfigValue('WEB_SEARCH_ENGINE', value)}
              >
                <SelectTrigger className="bg-white dark:bg-gray-900">
                  <SelectValue placeholder="Select a engine" />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-gray-900">
                  {webSearchEngines.map((engine) => (
                    <SelectItem key={engine} value={engine}>
                      {engine}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Engine-specific Configuration */}
            {webConfig.WEB_SEARCH_ENGINE && (
              <div className="space-y-4">
                {webConfig.WEB_SEARCH_ENGINE === 'searxng' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Searxng Query URL</Label>
                    <Input
                      value={webConfig.SEARXNG_QUERY_URL || ''}
                      onChange={(e) => updateConfigValue('SEARXNG_QUERY_URL', e.target.value)}
                      placeholder="Enter Searxng Query URL"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'yacy' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Yacy Instance URL</Label>
                      <Input
                        value={webConfig.YACY_QUERY_URL || ''}
                        onChange={(e) => updateConfigValue('YACY_QUERY_URL', e.target.value)}
                        placeholder="Enter Yacy URL (e.g. http://yacy.example.com:8090)"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-xs font-medium">Yacy Username</Label>
                        <Input
                          value={webConfig.YACY_USERNAME || ''}
                          onChange={(e) => updateConfigValue('YACY_USERNAME', e.target.value)}
                          placeholder="Enter Yacy Username"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-xs font-medium">Yacy Password</Label>
                        <SensitiveInput
                          value={webConfig.YACY_PASSWORD || ''}
                          onChange={(value) => updateConfigValue('YACY_PASSWORD', value)}
                          placeholder="Enter Yacy Password"
                        />
                      </div>
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'google_pse' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Google PSE API Key</Label>
                      <SensitiveInput
                        value={webConfig.GOOGLE_PSE_API_KEY || ''}
                        onChange={(value) => updateConfigValue('GOOGLE_PSE_API_KEY', value)}
                        placeholder="Enter Google PSE API Key"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Google PSE Engine Id</Label>
                      <Input
                        value={webConfig.GOOGLE_PSE_ENGINE_ID || ''}
                        onChange={(e) => updateConfigValue('GOOGLE_PSE_ENGINE_ID', e.target.value)}
                        placeholder="Enter Google PSE Engine Id"
                      />
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'brave' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Brave Search API Key</Label>
                    <SensitiveInput
                      value={webConfig.BRAVE_SEARCH_API_KEY || ''}
                      onChange={(value) => updateConfigValue('BRAVE_SEARCH_API_KEY', value)}
                      placeholder="Enter Brave Search API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'kagi' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Kagi Search API Key</Label>
                    <SensitiveInput
                      value={webConfig.KAGI_SEARCH_API_KEY || ''}
                      onChange={(value) => updateConfigValue('KAGI_SEARCH_API_KEY', value)}
                      placeholder="Enter Kagi Search API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'mojeek' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Mojeek Search API Key</Label>
                    <SensitiveInput
                      value={webConfig.MOJEEK_SEARCH_API_KEY || ''}
                      onChange={(value) => updateConfigValue('MOJEEK_SEARCH_API_KEY', value)}
                      placeholder="Enter Mojeek Search API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'bocha' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Bocha Search API Key</Label>
                    <SensitiveInput
                      value={webConfig.BOCHA_SEARCH_API_KEY || ''}
                      onChange={(value) => updateConfigValue('BOCHA_SEARCH_API_KEY', value)}
                      placeholder="Enter Bocha Search API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'serpstack' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Serpstack API Key</Label>
                    <SensitiveInput
                      value={webConfig.SERPSTACK_API_KEY || ''}
                      onChange={(value) => updateConfigValue('SERPSTACK_API_KEY', value)}
                      placeholder="Enter Serpstack API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'serper' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Serper API Key</Label>
                    <SensitiveInput
                      value={webConfig.SERPER_API_KEY || ''}
                      onChange={(value) => updateConfigValue('SERPER_API_KEY', value)}
                      placeholder="Enter Serper API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'serply' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Serply API Key</Label>
                    <SensitiveInput
                      value={webConfig.SERPLY_API_KEY || ''}
                      onChange={(value) => updateConfigValue('SERPLY_API_KEY', value)}
                      placeholder="Enter Serply API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'tavily' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Tavily API Key</Label>
                    <SensitiveInput
                      value={webConfig.TAVILY_API_KEY || ''}
                      onChange={(value) => updateConfigValue('TAVILY_API_KEY', value)}
                      placeholder="Enter Tavily API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'searchapi' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">SearchApi API Key</Label>
                      <SensitiveInput
                        value={webConfig.SEARCHAPI_API_KEY || ''}
                        onChange={(value) => updateConfigValue('SEARCHAPI_API_KEY', value)}
                        placeholder="Enter SearchApi API Key"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">SearchApi Engine</Label>
                      <Input
                        value={webConfig.SEARCHAPI_ENGINE || ''}
                        onChange={(e) => updateConfigValue('SEARCHAPI_ENGINE', e.target.value)}
                        placeholder="Enter SearchApi Engine"
                      />
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'serpapi' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">SerpApi API Key</Label>
                      <SensitiveInput
                        value={webConfig.SERPAPI_API_KEY || ''}
                        onChange={(value) => updateConfigValue('SERPAPI_API_KEY', value)}
                        placeholder="Enter SerpApi API Key"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">SerpApi Engine</Label>
                      <Input
                        value={webConfig.SERPAPI_ENGINE || ''}
                        onChange={(e) => updateConfigValue('SERPAPI_ENGINE', e.target.value)}
                        placeholder="Enter SerpApi Engine"
                      />
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'jina' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Jina API Key</Label>
                    <SensitiveInput
                      value={webConfig.JINA_API_KEY || ''}
                      onChange={(value) => updateConfigValue('JINA_API_KEY', value)}
                      placeholder="Enter Jina API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'bing' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Bing Search V7 Endpoint</Label>
                      <Input
                        value={webConfig.BING_SEARCH_V7_ENDPOINT || ''}
                        onChange={(e) => updateConfigValue('BING_SEARCH_V7_ENDPOINT', e.target.value)}
                        placeholder="Enter Bing Search V7 Endpoint"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Bing Search V7 Subscription Key</Label>
                      <SensitiveInput
                        value={webConfig.BING_SEARCH_V7_SUBSCRIPTION_KEY || ''}
                        onChange={(value) => updateConfigValue('BING_SEARCH_V7_SUBSCRIPTION_KEY', value)}
                        placeholder="Enter Bing Search V7 Subscription Key"
                      />
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'exa' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Exa API Key</Label>
                    <SensitiveInput
                      value={webConfig.EXA_API_KEY || ''}
                      onChange={(value) => updateConfigValue('EXA_API_KEY', value)}
                      placeholder="Enter Exa API Key"
                    />
                  </div>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'perplexity' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Perplexity API Key</Label>
                      <SensitiveInput
                        value={webConfig.PERPLEXITY_API_KEY || ''}
                        onChange={(value) => updateConfigValue('PERPLEXITY_API_KEY', value)}
                        placeholder="Enter Perplexity API Key"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Perplexity Model</Label>
                      <Input
                        list="perplexity-model-list"
                        value={webConfig.PERPLEXITY_MODEL || ''}
                        onChange={(e) => updateConfigValue('PERPLEXITY_MODEL', e.target.value)}
                        placeholder="Select or enter Perplexity model"
                      />
                      <datalist id="perplexity-model-list">
                        <option value="sonar">Sonar</option>
                        <option value="sonar-pro">Sonar Pro</option>
                        <option value="sonar-reasoning">Sonar Reasoning</option>
                        <option value="sonar-reasoning-pro">Sonar Reasoning Pro</option>
                        <option value="sonar-deep-research">Sonar Deep Research</option>
                      </datalist>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Perplexity Search Context Usage</Label>
                      <Select
                        value={webConfig.PERPLEXITY_SEARCH_CONTEXT_USAGE || 'medium'}
                        onValueChange={(value) => updateConfigValue('PERPLEXITY_SEARCH_CONTEXT_USAGE', value)}
                      >
                        <SelectTrigger className="bg-white dark:bg-gray-900">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-white dark:bg-gray-900">
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'sougou' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Sougou Search API sID</Label>
                      <SensitiveInput
                        value={webConfig.SOUGOU_API_SID || ''}
                        onChange={(value) => updateConfigValue('SOUGOU_API_SID', value)}
                        placeholder="Enter Sougou Search API sID"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Sougou Search API SK</Label>
                      <SensitiveInput
                        value={webConfig.SOUGOU_API_SK || ''}
                        onChange={(value) => updateConfigValue('SOUGOU_API_SK', value)}
                        placeholder="Enter Sougou Search API SK"
                      />
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'firecrawl' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Firecrawl API Base URL</Label>
                      <Input
                        value={webConfig.FIRECRAWL_API_BASE_URL || ''}
                        onChange={(e) => updateConfigValue('FIRECRAWL_API_BASE_URL', e.target.value)}
                        placeholder="Enter Firecrawl API Base URL"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">Firecrawl API Key</Label>
                      <SensitiveInput
                        value={webConfig.FIRECRAWL_API_KEY || ''}
                        onChange={(value) => updateConfigValue('FIRECRAWL_API_KEY', value)}
                        placeholder="Enter Firecrawl API Key"
                      />
                    </div>
                  </>
                )}

                {webConfig.WEB_SEARCH_ENGINE === 'external' && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">External Web Search URL</Label>
                      <Input
                        value={webConfig.EXTERNAL_WEB_SEARCH_URL || ''}
                        onChange={(e) => updateConfigValue('EXTERNAL_WEB_SEARCH_URL', e.target.value)}
                        placeholder="Enter External Web Search URL"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium">External Web Search API Key</Label>
                      <SensitiveInput
                        value={webConfig.EXTERNAL_WEB_SEARCH_API_KEY || ''}
                        onChange={(value) => updateConfigValue('EXTERNAL_WEB_SEARCH_API_KEY', value)}
                        placeholder="Enter External Web Search API Key"
                      />
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Search Parameters */}
            {webConfig.ENABLE_WEB_SEARCH && (
              <>
                <Separator />
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Search Result Count</Label>
                    <Input
                      type="number"
                      value={webConfig.WEB_SEARCH_RESULT_COUNT || ''}
                      onChange={(e) => updateConfigValue('WEB_SEARCH_RESULT_COUNT', e.target.value)}
                      placeholder="Search Result Count"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Concurrent Requests</Label>
                    <Input
                      type="number"
                      value={webConfig.WEB_SEARCH_CONCURRENT_REQUESTS || ''}
                      onChange={(e) => updateConfigValue('WEB_SEARCH_CONCURRENT_REQUESTS', e.target.value)}
                      placeholder="Concurrent Requests"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs font-medium">Domain Filter List</Label>
                  <Input
                    value={webConfig.WEB_SEARCH_DOMAIN_FILTER_LIST || ''}
                    onChange={(e) => updateConfigValue('WEB_SEARCH_DOMAIN_FILTER_LIST', e.target.value)}
                    placeholder="Enter domains separated by commas (e.g., example.com,site.org)"
                  />
                </div>
              </>
            )}

            <Separator />

            {/* Advanced Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Tooltip content="Full Context Mode">
                    <Label className="text-xs font-medium">Bypass Embedding and Retrieval</Label>
                  </Tooltip>
                </div>
                <Tooltip
                  content={webConfig.BYPASS_WEB_SEARCH_EMBEDDING_AND_RETRIEVAL
                    ? "Inject the entire content as context for comprehensive processing, this is recommended for complex queries."
                    : "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases."
                  }
                >
                  <Switch
                    checked={webConfig.BYPASS_WEB_SEARCH_EMBEDDING_AND_RETRIEVAL || false}
                    onCheckedChange={(checked) => updateConfigValue('BYPASS_WEB_SEARCH_EMBEDDING_AND_RETRIEVAL', checked)}
                  />
                </Tooltip>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Tooltip content="Bypass Web Loader">
                    <Label className="text-xs font-medium">Bypass Web Loader</Label>
                  </Tooltip>
                </div>
                <Switch
                  checked={webConfig.BYPASS_WEB_SEARCH_WEB_LOADER || false}
                  onCheckedChange={(checked) => updateConfigValue('BYPASS_WEB_SEARCH_WEB_LOADER', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Trust Proxy Environment</Label>
                <Tooltip
                  content={webConfig.WEB_SEARCH_TRUST_ENV
                    ? "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents."
                    : "Use no proxy to fetch page contents."
                  }
                >
                  <Switch
                    checked={webConfig.WEB_SEARCH_TRUST_ENV || false}
                    onCheckedChange={(checked) => updateConfigValue('WEB_SEARCH_TRUST_ENV', checked)}
                  />
                </Tooltip>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loader Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Loader</CardTitle>
            <CardDescription>
              Configure web content loading engines and settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Web Loader Engine */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Web Loader Engine</Label>
              <Select
                value={webConfig.WEB_LOADER_ENGINE || ''}
                onValueChange={(value) => updateConfigValue('WEB_LOADER_ENGINE', value)}
              >
                <SelectTrigger className="bg-white dark:bg-gray-900">
                  <SelectValue placeholder="Default" />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-gray-900">
                  <SelectItem value="default">Default</SelectItem>
                  {webLoaderEngines.map((engine) => (
                    <SelectItem key={engine} value={engine}>
                      {engine}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Engine-specific Loader Configuration */}
            {(webConfig.WEB_LOADER_ENGINE === 'default' || webConfig.WEB_LOADER_ENGINE === 'safe_web' || !webConfig.WEB_LOADER_ENGINE) && (
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Verify SSL Certificate</Label>
                <Switch
                  checked={webConfig.ENABLE_WEB_LOADER_SSL_VERIFICATION || false}
                  onCheckedChange={(checked) => updateConfigValue('ENABLE_WEB_LOADER_SSL_VERIFICATION', checked)}
                />
              </div>
            )}

            {webConfig.WEB_LOADER_ENGINE === 'playwright' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Playwright WebSocket URL</Label>
                  <Input
                    value={webConfig.PLAYWRIGHT_WS_URL || ''}
                    onChange={(e) => updateConfigValue('PLAYWRIGHT_WS_URL', e.target.value)}
                    placeholder="Enter Playwright WebSocket URL"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Playwright Timeout (ms)</Label>
                  <Input
                    type="number"
                    value={webConfig.PLAYWRIGHT_TIMEOUT || ''}
                    onChange={(e) => updateConfigValue('PLAYWRIGHT_TIMEOUT', e.target.value)}
                    placeholder="Enter Playwright Timeout"
                  />
                </div>
              </div>
            )}

            {webConfig.WEB_LOADER_ENGINE === 'firecrawl' && webConfig.WEB_SEARCH_ENGINE !== 'firecrawl' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Firecrawl API Base URL</Label>
                  <Input
                    value={webConfig.FIRECRAWL_API_BASE_URL || ''}
                    onChange={(e) => updateConfigValue('FIRECRAWL_API_BASE_URL', e.target.value)}
                    placeholder="Enter Firecrawl API Base URL"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Firecrawl API Key</Label>
                  <SensitiveInput
                    value={webConfig.FIRECRAWL_API_KEY || ''}
                    onChange={(value) => updateConfigValue('FIRECRAWL_API_KEY', value)}
                    placeholder="Enter Firecrawl API Key"
                  />
                </div>
              </div>
            )}

            {webConfig.WEB_LOADER_ENGINE === 'tavily' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Tavily Extract Depth</Label>
                  <Input
                    value={webConfig.TAVILY_EXTRACT_DEPTH || ''}
                    onChange={(e) => updateConfigValue('TAVILY_EXTRACT_DEPTH', e.target.value)}
                    placeholder="Enter Tavily Extract Depth"
                  />
                </div>
                {webConfig.WEB_SEARCH_ENGINE !== 'tavily' && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Tavily API Key</Label>
                    <SensitiveInput
                      value={webConfig.TAVILY_API_KEY || ''}
                      onChange={(value) => updateConfigValue('TAVILY_API_KEY', value)}
                      placeholder="Enter Tavily API Key"
                    />
                  </div>
                )}
              </div>
            )}

            {webConfig.WEB_LOADER_ENGINE === 'external' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-xs font-medium">External Web Loader URL</Label>
                  <Input
                    value={webConfig.EXTERNAL_WEB_LOADER_URL || ''}
                    onChange={(e) => updateConfigValue('EXTERNAL_WEB_LOADER_URL', e.target.value)}
                    placeholder="Enter External Web Loader URL"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs font-medium">External Web Loader API Key</Label>
                  <SensitiveInput
                    value={webConfig.EXTERNAL_WEB_LOADER_API_KEY || ''}
                    onChange={(value) => updateConfigValue('EXTERNAL_WEB_LOADER_API_KEY', value)}
                    placeholder="Enter External Web Loader API Key"
                  />
                </div>
              </div>
            )}

            <Separator />

            {/* YouTube Configuration */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-xs font-medium">Youtube Language</Label>
                <Input
                  value={webConfig.YOUTUBE_LOADER_LANGUAGE || ''}
                  onChange={(e) => updateConfigValue('YOUTUBE_LOADER_LANGUAGE', e.target.value)}
                  placeholder="Enter language codes"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-xs font-medium">Youtube Proxy URL</Label>
                <Input
                  value={webConfig.YOUTUBE_LOADER_PROXY_URL || ''}
                  onChange={(e) => updateConfigValue('YOUTUBE_LOADER_PROXY_URL', e.target.value)}
                  placeholder="Enter proxy URL (e.g. **************************:port)"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end">
        <Button 
          type="submit" 
          disabled={isSaving}
          className="min-w-[120px]"
        >
          {isSaving ? <Spinner size="sm" /> : 'Save'}
        </Button>
      </div>
    </form>
  );
}