import React from 'react';

interface AudioProps {
  saveHandler?: () => void;
}

export default function Audio({ saveHandler }: AudioProps) {
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Audio Settings
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        Audio settings content will be implemented here.
      </p>
    </div>
  );
}