import React from 'react';

interface ConnectionsProps {
  onSave?: () => void;
}

export default function Connections({ onSave }: ConnectionsProps) {
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Connections Settings
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        Connections settings content will be implemented here.
      </p>
    </div>
  );
}