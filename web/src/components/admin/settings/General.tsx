'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import { toast } from 'sonner';
import { Spinner } from '@/components/ui/spinner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { getBackendConfig } from '@/lib/api';
import { getVersionUpdates, getWebhookUrl, updateWebhookUrl } from '@/lib/apis';
import { getAdminConfig, updateAdminConfig } from '@/lib/apis/auths';

interface GeneralProps {
  saveHandler?: () => void;
}

interface AdminConfig {
  ENABLE_SIGNUP: boolean;
  ENABLE_LOGIN_FORM: boolean;
  ENABLE_MESSAGE_RATING: boolean;
  ENABLE_COMMUNITY_SHARING: boolean;
  ENABLE_NEW_SIGN_UPS: boolean;
  DEFAULT_USER_ROLE: string;
  DEFAULT_MODELS: string;
  DEFAULT_PROMPT_SUGGESTIONS: any[];
  WEBUI_NAME: string;
  WEBUI_URL: string;
  WEBUI_SECRET_KEY: string;
  WEBUI_AUTH: boolean;
  WEBUI_AUTH_TRUSTED_EMAIL_HEADER: string | null;
  [key: string]: any;
}

export default function General({ saveHandler }: GeneralProps) {
  const { token } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [adminConfig, setAdminConfig] = useState<AdminConfig | null>(null);
  const [webhookUrl, setWebhookUrl] = useState('');
  const [updateAvailable, setUpdateAvailable] = useState(null);
  const [version, setVersion] = useState({
    current: '',
    latest: ''
  });

  useEffect(() => {
    if (token) {
      loadSettings();
    }
  }, [token]);

  const loadSettings = async () => {
    if (!token) return;

    setIsLoading(true);
    try {
      // Load admin config, webhook URL, and check for updates
      const [configRes, webhookRes, versionRes] = await Promise.allSettled([
        getAdminConfig(token),
        getWebhookUrl(token),
        getVersionUpdates(token)
      ]);

      if (configRes.status === 'fulfilled') {
        setAdminConfig(configRes.value);
      }

      if (webhookRes.status === 'fulfilled') {
        setWebhookUrl(webhookRes.value || '');
      }

      if (versionRes.status === 'fulfilled') {
        setVersion({
          current: versionRes.value?.current || '',
          latest: versionRes.value?.latest || ''
        });
        setUpdateAvailable(versionRes.value?.update_available || null);
      }
    } catch (error) {
      console.error('Failed to load general settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!token || !adminConfig) return;

    setIsSaving(true);
    try {
      // Update webhook URL
      if (webhookUrl !== '') {
        await updateWebhookUrl(token, webhookUrl);
      }

      // Update admin config
      const result = await updateAdminConfig(token, adminConfig);
      
      if (result) {
        toast.success('Settings saved successfully!');
        if (saveHandler) {
          saveHandler();
        }
      } else {
        toast.error('Failed to update settings');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const updateConfigValue = (key: string, value: any) => {
    if (adminConfig) {
      setAdminConfig({
        ...adminConfig,
        [key]: value
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!adminConfig) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Failed to load configuration
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Version Information */}
      {updateAvailable && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Update Available
              <Badge variant="secondary">v{version.latest}</Badge>
            </CardTitle>
            <CardDescription>
              A new version is available. Current version: {version.current}
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* General Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>General Configuration</CardTitle>
          <CardDescription>
            Basic application settings and user management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Application Name */}
          <div className="space-y-2">
            <Label htmlFor="webui-name">Application Name</Label>
            <Input
              id="webui-name"
              value={adminConfig.WEBUI_NAME || ''}
              onChange={(e) => updateConfigValue('WEBUI_NAME', e.target.value)}
              placeholder="Open WebUI"
            />
          </div>

          <Separator />

          {/* User Management */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">User Management</h4>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable New Sign Ups</Label>
                <p className="text-sm text-gray-500">Allow new users to register</p>
              </div>
              <Switch
                checked={adminConfig.ENABLE_SIGNUP || false}
                onCheckedChange={(checked) => updateConfigValue('ENABLE_SIGNUP', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Login Form</Label>
                <p className="text-sm text-gray-500">Show login form on the interface</p>
              </div>
              <Switch
                checked={adminConfig.ENABLE_LOGIN_FORM !== false}
                onCheckedChange={(checked) => updateConfigValue('ENABLE_LOGIN_FORM', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="default-role">Default User Role</Label>
              <select
                id="default-role"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                value={adminConfig.DEFAULT_USER_ROLE || 'pending'}
                onChange={(e) => updateConfigValue('DEFAULT_USER_ROLE', e.target.value)}
              >
                <option value="pending">Pending</option>
                <option value="user">User</option>
                <option value="admin">Admin</option>
              </select>
            </div>
          </div>

          <Separator />

          {/* Chat Features */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Chat Features</h4>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Message Rating</Label>
                <p className="text-sm text-gray-500">Allow users to rate messages</p>
              </div>
              <Switch
                checked={adminConfig.ENABLE_MESSAGE_RATING !== false}
                onCheckedChange={(checked) => updateConfigValue('ENABLE_MESSAGE_RATING', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Community Sharing</Label>
                <p className="text-sm text-gray-500">Allow sharing chats with community</p>
              </div>
              <Switch
                checked={adminConfig.ENABLE_COMMUNITY_SHARING || false}
                onCheckedChange={(checked) => updateConfigValue('ENABLE_COMMUNITY_SHARING', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Webhook Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Webhook Configuration</CardTitle>
          <CardDescription>
            Configure webhook URL for external integrations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="webhook-url">Webhook URL</Label>
            <Input
              id="webhook-url"
              type="url"
              value={webhookUrl}
              onChange={(e) => setWebhookUrl(e.target.value)}
              placeholder="https://example.com/webhook"
            />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button 
          onClick={handleSave} 
          disabled={isSaving}
          className="min-w-[120px]"
        >
          {isSaving ? <Spinner size="sm" /> : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
}