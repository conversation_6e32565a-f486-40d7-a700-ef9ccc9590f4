import React from 'react';

interface ImagesProps {
  onSave?: () => void;
}

export default function Images({ onSave }: ImagesProps) {
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Images Settings
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        Images settings content will be implemented here.
      </p>
    </div>
  );
}