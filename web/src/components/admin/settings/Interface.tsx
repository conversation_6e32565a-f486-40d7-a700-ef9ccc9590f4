'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { Spinner } from '@/components/ui/spinner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Upload, Download, X, HelpCircle } from 'lucide-react';
import { Tooltip } from '@/components/ui/tooltip';

import { getBackendConfig, getModels } from '@/lib/api';
import { getBaseModels } from '@/lib/apis/models';
import { 
  getTaskConfig, 
  updateTaskConfig, 
  setDefaultPromptSuggestions,
  getBanners,
  setBanners,
  Banner 
} from '@/lib/apis/configs';

interface InterfaceProps {
  onSave?: () => void;
}

interface TaskConfig {
  TASK_MODEL: string;
  TASK_MODEL_EXTERNAL: string;
  ENABLE_TITLE_GENERATION: boolean;
  TITLE_GENERATION_PROMPT_TEMPLATE: string;
  ENABLE_FOLLOW_UP_GENERATION: boolean;
  FOLLOW_UP_GENERATION_PROMPT_TEMPLATE: string;
  IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE: string;
  ENABLE_AUTOCOMPLETE_GENERATION: boolean;
  AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH: number;
  TAGS_GENERATION_PROMPT_TEMPLATE: string;
  ENABLE_TAGS_GENERATION: boolean;
  ENABLE_SEARCH_QUERY_GENERATION: boolean;
  ENABLE_RETRIEVAL_QUERY_GENERATION: boolean;
  QUERY_GENERATION_PROMPT_TEMPLATE: string;
  TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE: string;
}

interface PromptSuggestion {
  content: string;
  title: [string, string];
}

interface Model {
  id: string;
  name: string;
  connection_type?: string;
  access_control?: any;
  is_active?: boolean;
}

export default function Interface({ onSave }: InterfaceProps) {
  const { token, user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  const [taskConfig, setTaskConfig] = useState<TaskConfig>({
    TASK_MODEL: '',
    TASK_MODEL_EXTERNAL: '',
    ENABLE_TITLE_GENERATION: true,
    TITLE_GENERATION_PROMPT_TEMPLATE: '',
    ENABLE_FOLLOW_UP_GENERATION: true,
    FOLLOW_UP_GENERATION_PROMPT_TEMPLATE: '',
    IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE: '',
    ENABLE_AUTOCOMPLETE_GENERATION: true,
    AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH: -1,
    TAGS_GENERATION_PROMPT_TEMPLATE: '',
    ENABLE_TAGS_GENERATION: true,
    ENABLE_SEARCH_QUERY_GENERATION: true,
    ENABLE_RETRIEVAL_QUERY_GENERATION: true,
    QUERY_GENERATION_PROMPT_TEMPLATE: '',
    TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE: ''
  });

  const [promptSuggestions, setPromptSuggestions] = useState<PromptSuggestion[]>([]);
  const [banners, setBannersState] = useState<Banner[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [config, setConfig] = useState<any>(null);

  useEffect(() => {
    if (token) {
      loadData();
    }
  }, [token]);

  const loadData = async () => {
    if (!token) return;

    setIsLoading(true);
    try {
      // Load all required data
      const [configRes, taskRes, workspaceModels, baseModels, bannersRes] = await Promise.allSettled([
        getBackendConfig(),
        getTaskConfig(token),
        getBaseModels(token),
        getModels(token, null, false),
        getBanners(token)
      ]);

      if (configRes.status === 'fulfilled') {
        setConfig(configRes.value);
        setPromptSuggestions(configRes.value?.default_prompt_suggestions || []);
      }

      if (taskRes.status === 'fulfilled' && taskRes.value) {
        setTaskConfig(taskRes.value);
      }

      if (workspaceModels.status === 'fulfilled' && baseModels.status === 'fulfilled') {
        const workspaceModelsList = workspaceModels.value || [];
        const baseModelsList = baseModels.value || [];

        const combinedModels = baseModelsList.map((m: any) => {
          const workspaceModel = workspaceModelsList.find((wm: any) => wm.id === m.id);
          
          if (workspaceModel) {
            return {
              ...m,
              ...workspaceModel
            };
          } else {
            return {
              ...m,
              id: m.id,
              name: m.name,
              is_active: true
            };
          }
        });

        setModels(combinedModels);
      }

      if (bannersRes.status === 'fulfilled') {
        setBannersState(bannersRes.value || []);
      }
    } catch (error) {
      console.error('Failed to load interface settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!token) return;

    setIsSaving(true);
    try {
      // Update task config
      await updateTaskConfig(token, taskConfig);

      // Update prompt suggestions
      const filteredSuggestions = promptSuggestions.filter((p) => p.content !== '');
      await setDefaultPromptSuggestions(token, filteredSuggestions);

      // Update banners
      await setBanners(token, banners);

      // Reload backend config
      await getBackendConfig();

      toast.success('Interface settings saved successfully!');
      if (onSave) {
        onSave();
      }
    } catch (error) {
      console.error('Failed to save interface settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const updateTaskConfigValue = (key: keyof TaskConfig, value: any) => {
    setTaskConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const addPromptSuggestion = () => {
    if (promptSuggestions.length === 0 || promptSuggestions.at(-1)?.content !== '') {
      setPromptSuggestions([...promptSuggestions, { content: '', title: ['', ''] }]);
    }
  };

  const updatePromptSuggestion = (index: number, field: string, value: any) => {
    const updated = [...promptSuggestions];
    if (field === 'title0') {
      updated[index].title[0] = value;
    } else if (field === 'title1') {
      updated[index].title[1] = value;
    } else {
      (updated[index] as any)[field] = value;
    }
    setPromptSuggestions(updated);
  };

  const removePromptSuggestion = (index: number) => {
    const updated = [...promptSuggestions];
    updated.splice(index, 1);
    setPromptSuggestions(updated);
  };

  const addBanner = () => {
    if (banners.length === 0 || banners.at(-1)?.content !== '') {
      setBannersState([
        ...banners,
        {
          id: uuidv4(),
          type: '',
          title: '',
          content: '',
          dismissible: true,
          timestamp: Math.floor(Date.now() / 1000)
        }
      ]);
    }
  };

  const updateBanner = (index: number, field: keyof Banner, value: any) => {
    const updated = [...banners];
    (updated[index] as any)[field] = value;
    setBannersState(updated);
  };

  const removeBanner = (index: number) => {
    const updated = [...banners];
    updated.splice(index, 1);
    setBannersState(updated);
  };

  const handleModelChange = (modelId: string, isExternal: boolean) => {
    if (modelId) {
      const model = models.find((m) => m.id === modelId);
      if (model && model.access_control !== null) {
        toast.error('This model is not publicly available. Please select another model.');
        return;
      }
    }
    
    if (isExternal) {
      updateTaskConfigValue('TASK_MODEL_EXTERNAL', modelId);
    } else {
      updateTaskConfigValue('TASK_MODEL', modelId);
    }
  };

  const importPromptSuggestions = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (!files || files.length === 0) return;

      const reader = new FileReader();
      reader.onload = async (event) => {
        try {
          let suggestions = JSON.parse(event.target?.result as string);
          
          suggestions = suggestions.map((s: any) => {
            if (typeof s.title === 'string') {
              s.title = [s.title, ''];
            } else if (!Array.isArray(s.title)) {
              s.title = ['', ''];
            }
            return s;
          });

          setPromptSuggestions([...promptSuggestions, ...suggestions]);
        } catch (error) {
          toast.error('Invalid JSON file');
        }
      };
      reader.readAsText(files[0]);
    };
    input.click();
  };

  const exportPromptSuggestions = () => {
    const blob = new Blob([JSON.stringify(promptSuggestions, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `prompt-suggestions-export-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <form
      className="flex flex-col h-full justify-between space-y-3 text-sm"
      onSubmit={(e) => {
        e.preventDefault();
        handleSave();
      }}
    >
        <div className="overflow-y-auto scrollbar-hidden h-full pr-1.5 space-y-6">
          {/* Tasks Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Tasks</CardTitle>
              <CardDescription>
                Configure task models and generation settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Task Model Selection */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Label className="text-xs font-medium">Task Model</Label>
                  <Tooltip content="A task model is used when performing tasks such as generating titles for chats and web search queries">
                    <HelpCircle className="h-3.5 w-3.5" />
                  </Tooltip>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="local-task-model" className="text-xs">Local Task Model</Label>
                    <Select
                      value={taskConfig.TASK_MODEL}
                      onValueChange={(value) => handleModelChange(value, false)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Current Model" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Current Model</SelectItem>
                        {models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.name}
                            {model.connection_type === 'local' ? ' (Local)' : ''}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="external-task-model" className="text-xs">External Task Model</Label>
                    <Select
                      value={taskConfig.TASK_MODEL_EXTERNAL}
                      onValueChange={(value) => handleModelChange(value, true)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Current Model" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Current Model</SelectItem>
                        {models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.name}
                            {model.connection_type === 'local' ? ' (Local)' : ''}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Generation Settings */}
              <div className="space-y-4">
                {/* Title Generation */}
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Title Generation</Label>
                  <Switch
                    checked={taskConfig.ENABLE_TITLE_GENERATION}
                    onCheckedChange={(checked) => updateTaskConfigValue('ENABLE_TITLE_GENERATION', checked)}
                  />
                </div>

                {taskConfig.ENABLE_TITLE_GENERATION && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Title Generation Prompt</Label>
                    <Tooltip content="Leave empty to use the default prompt, or enter a custom prompt">
                      <Textarea
                        value={taskConfig.TITLE_GENERATION_PROMPT_TEMPLATE}
                        onChange={(e) => updateTaskConfigValue('TITLE_GENERATION_PROMPT_TEMPLATE', e.target.value)}
                        placeholder="Leave empty to use the default prompt, or enter a custom prompt"
                        rows={3}
                      />
                    </Tooltip>
                  </div>
                )}

                {/* Follow Up Generation */}
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Follow Up Generation</Label>
                  <Switch
                    checked={taskConfig.ENABLE_FOLLOW_UP_GENERATION}
                    onCheckedChange={(checked) => updateTaskConfigValue('ENABLE_FOLLOW_UP_GENERATION', checked)}
                  />
                </div>

                {taskConfig.ENABLE_FOLLOW_UP_GENERATION && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Follow Up Generation Prompt</Label>
                    <Tooltip content="Leave empty to use the default prompt, or enter a custom prompt">
                      <Textarea
                        value={taskConfig.FOLLOW_UP_GENERATION_PROMPT_TEMPLATE}
                        onChange={(e) => updateTaskConfigValue('FOLLOW_UP_GENERATION_PROMPT_TEMPLATE', e.target.value)}
                        placeholder="Leave empty to use the default prompt, or enter a custom prompt"
                        rows={3}
                      />
                    </Tooltip>
                  </div>
                )}

                {/* Tags Generation */}
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Tags Generation</Label>
                  <Switch
                    checked={taskConfig.ENABLE_TAGS_GENERATION}
                    onCheckedChange={(checked) => updateTaskConfigValue('ENABLE_TAGS_GENERATION', checked)}
                  />
                </div>

                {taskConfig.ENABLE_TAGS_GENERATION && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Tags Generation Prompt</Label>
                    <Tooltip content="Leave empty to use the default prompt, or enter a custom prompt">
                      <Textarea
                        value={taskConfig.TAGS_GENERATION_PROMPT_TEMPLATE}
                        onChange={(e) => updateTaskConfigValue('TAGS_GENERATION_PROMPT_TEMPLATE', e.target.value)}
                        placeholder="Leave empty to use the default prompt, or enter a custom prompt"
                        rows={3}
                      />
                    </Tooltip>
                  </div>
                )}

                {/* Query Generation Settings */}
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Retrieval Query Generation</Label>
                  <Switch
                    checked={taskConfig.ENABLE_RETRIEVAL_QUERY_GENERATION}
                    onCheckedChange={(checked) => updateTaskConfigValue('ENABLE_RETRIEVAL_QUERY_GENERATION', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Web Search Query Generation</Label>
                  <Switch
                    checked={taskConfig.ENABLE_SEARCH_QUERY_GENERATION}
                    onCheckedChange={(checked) => updateTaskConfigValue('ENABLE_SEARCH_QUERY_GENERATION', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-xs font-medium">Query Generation Prompt</Label>
                  <Tooltip content="Leave empty to use the default prompt, or enter a custom prompt">
                    <Textarea
                      value={taskConfig.QUERY_GENERATION_PROMPT_TEMPLATE}
                      onChange={(e) => updateTaskConfigValue('QUERY_GENERATION_PROMPT_TEMPLATE', e.target.value)}
                      placeholder="Leave empty to use the default prompt, or enter a custom prompt"
                      rows={3}
                    />
                  </Tooltip>
                </div>

                {/* Autocomplete Generation */}
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Autocomplete Generation</Label>
                  <Tooltip content="Enable autocomplete generation for chat messages">
                    <Switch
                      checked={taskConfig.ENABLE_AUTOCOMPLETE_GENERATION}
                      onCheckedChange={(checked) => updateTaskConfigValue('ENABLE_AUTOCOMPLETE_GENERATION', checked)}
                    />
                  </Tooltip>
                </div>

                {taskConfig.ENABLE_AUTOCOMPLETE_GENERATION && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Autocomplete Generation Input Max Length</Label>
                    <Tooltip content="Character limit for autocomplete generation input">
                      <Input
                        type="number"
                        value={taskConfig.AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH}
                        onChange={(e) => updateTaskConfigValue('AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH', parseInt(e.target.value) || -1)}
                        placeholder="-1 for no limit, or a positive integer for a specific limit"
                      />
                    </Tooltip>
                  </div>
                )}

                {/* Additional Prompts */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Image Prompt Generation Prompt</Label>
                  <Tooltip content="Leave empty to use the default prompt, or enter a custom prompt">
                    <Textarea
                      value={taskConfig.IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE}
                      onChange={(e) => updateTaskConfigValue('IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE', e.target.value)}
                      placeholder="Leave empty to use the default prompt, or enter a custom prompt"
                      rows={3}
                    />
                  </Tooltip>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs font-medium">Tools Function Calling Prompt</Label>
                  <Tooltip content="Leave empty to use the default prompt, or enter a custom prompt">
                    <Textarea
                      value={taskConfig.TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE}
                      onChange={(e) => updateTaskConfigValue('TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE', e.target.value)}
                      placeholder="Leave empty to use the default prompt, or enter a custom prompt"
                      rows={3}
                    />
                  </Tooltip>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* UI Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>UI</CardTitle>
              <CardDescription>
                Configure user interface elements and default settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Banners */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label className="text-sm">Banners</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addBanner}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {banners.map((banner, index) => (
                  <div key={banner.id} className="border rounded-xl p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div className="grid grid-cols-2 gap-4 flex-1">
                        <div className="space-y-2">
                          <Label className="text-xs">Type</Label>
                          <Select
                            value={banner.type}
                            onValueChange={(value) => updateBanner(index, 'type', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="">None</SelectItem>
                              <SelectItem value="info">Info</SelectItem>
                              <SelectItem value="warning">Warning</SelectItem>
                              <SelectItem value="error">Error</SelectItem>
                              <SelectItem value="success">Success</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label className="text-xs">Title</Label>
                          <Input
                            value={banner.title}
                            onChange={(e) => updateBanner(index, 'title', e.target.value)}
                            placeholder="Banner title"
                          />
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeBanner(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs">Content</Label>
                      <Textarea
                        value={banner.content}
                        onChange={(e) => updateBanner(index, 'content', e.target.value)}
                        placeholder="Banner content"
                        rows={3}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={banner.dismissible}
                        onCheckedChange={(checked) => updateBanner(index, 'dismissible', checked)}
                      />
                      <Label className="text-xs">Dismissible</Label>
                    </div>
                  </div>
                ))}
              </div>

              {/* Default Prompt Suggestions */}
              {user?.role === 'admin' && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <Label className="text-sm">Default Prompt Suggestions</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addPromptSuggestion}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-4">
                    {promptSuggestions.map((prompt, index) => (
                      <div
                        key={index}
                        className="border rounded-xl p-4 space-y-3"
                      >
                        <div className="flex space-x-2">
                          <Input
                            value={prompt.title[0]}
                            onChange={(e) => updatePromptSuggestion(index, 'title0', e.target.value)}
                            placeholder="Title (e.g. Tell me a fun fact)"
                            className="flex-1"
                          />
                          <Input
                            value={prompt.title[1]}
                            onChange={(e) => updatePromptSuggestion(index, 'title1', e.target.value)}
                            placeholder="Subtitle (e.g. about the Roman Empire)"
                            className="flex-1"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removePromptSuggestion(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <Textarea
                          value={prompt.content}
                          onChange={(e) => updatePromptSuggestion(index, 'content', e.target.value)}
                          placeholder="Prompt (e.g. Tell me a fun fact about the Roman Empire)"
                          rows={3}
                        />
                      </div>
                    ))}
                  </div>

                  {promptSuggestions.length > 0 && (
                    <div className="text-xs text-left w-full">
                      Adjusting these settings will apply changes universally to all users.
                    </div>
                  )}

                  <div className="flex items-center justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={importPromptSuggestions}
                    >
                      <Upload className="h-3.5 w-3.5 mr-2" />
                      Import Prompt Suggestions
                    </Button>

                    {promptSuggestions.length > 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={exportPromptSuggestions}
                      >
                        <Download className="h-3.5 w-3.5 mr-2" />
                        Export Prompt Suggestions ({promptSuggestions.length})
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end">
          <Button 
            type="submit" 
            disabled={isSaving}
            className="min-w-[120px]"
          >
            {isSaving ? <Spinner size="sm" /> : 'Save'}
          </Button>
        </div>
      </form>
    );
}