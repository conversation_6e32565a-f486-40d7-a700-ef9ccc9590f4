import React from 'react';

interface PipelinesProps {
  saveHandler?: () => void;
}

export default function Pipelines({ saveHandler }: PipelinesProps) {
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Pipelines Settings
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        Pipelines settings content will be implemented here.
      </p>
    </div>
  );
}