import { RETRIEVAL_API_BASE_URL } from '@/lib/constants';

export const getRAGConfig = async (token: string) => {
  let error = null;

  const res = await fetch(`${RETRIEVAL_API_BASE_URL}/config`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    }
  })
    .then(async (res) => {
      if (!res.ok) throw await res.json();
      return res.json();
    })
    .catch((err) => {
      console.log(err);
      error = err.detail;
      return null;
    });

  if (error) {
    throw error;
  }

  return res;
};

type RAGConfigForm = {
  web?: any;
  [key: string]: any;
};

export const updateRAGConfig = async (token: string, payload: RAGConfigForm) => {
  let error = null;

  const res = await fetch(`${RETRIEVAL_API_BASE_URL}/config/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    },
    body: JSON.stringify({
      ...payload
    })
  })
    .then(async (res) => {
      if (!res.ok) throw await res.json();
      return res.json();
    })
    .catch((err) => {
      console.log(err);
      error = err.detail;
      return null;
    });

  if (error) {
    throw error;
  }

  return res;
};