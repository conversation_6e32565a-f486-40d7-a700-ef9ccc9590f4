import { WEB<PERSON>_BASE_URL } from '@/lib/constants';

// Version updates API
export const getVersionUpdates = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/version/updates`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      console.warn(`Failed to get version updates: HTTP ${response.status}`);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to fetch version updates:', error);
    return null;
  }
};

// Webhook URL APIs
export const getWebhookUrl = async (token: string): Promise<string> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/webhook`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      console.warn(`Failed to get webhook URL: HTTP ${response.status}`);
      return '';
    }

    const result = await response.json();
    return result.url || '';
  } catch (error) {
    console.error('Failed to get webhook URL:', error);
    return '';
  }
};

export const updateWebhookUrl = async (token: string, url: string): Promise<string> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ url })
    });

    if (!response.ok) {
      console.warn(`Failed to update webhook URL: HTTP ${response.status}`);
      return url;
    }

    const result = await response.json();
    return result.url || url;
  } catch (error) {
    console.error('Failed to update webhook URL:', error);
    return url;
  }
};