import { WEBUI_API_BASE_URL } from '@/lib/constants';

export interface Banner {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success' | '';
  title: string;
  content: string;
  dismissible: boolean;
  timestamp: number;
}

export const setDefaultPromptSuggestions = async (token: string, promptSuggestions: any[]) => {
  let error = null;

  const res = await fetch(`${WEBUI_API_BASE_URL}/configs/suggestions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    },
    body: JSON.stringify({
      suggestions: promptSuggestions
    })
  })
    .then(async (res) => {
      if (!res.ok) throw await res.json();
      return res.json();
    })
    .catch((err) => {
      console.log(err);
      error = err.detail;
      return null;
    });

  if (error) {
    throw error;
  }

  return res;
};

export const getBanners = async (token: string): Promise<Banner[]> => {
  let error = null;

  const res = await fetch(`${WEBUI_API_BASE_URL}/configs/banners`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    }
  })
    .then(async (res) => {
      if (!res.ok) throw await res.json();
      return res.json();
    })
    .catch((err) => {
      console.log(err);
      error = err.detail;
      return null;
    });

  if (error) {
    throw error;
  }

  return res || [];
};

export const setBanners = async (token: string, banners: Banner[]) => {
  let error = null;

  const res = await fetch(`${WEBUI_API_BASE_URL}/configs/banners`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    },
    body: JSON.stringify({
      banners: banners
    })
  })
    .then(async (res) => {
      if (!res.ok) throw await res.json();
      return res.json();
    })
    .catch((err) => {
      console.log(err);
      error = err.detail;
      return null;
    });

  if (error) {
    throw error;
  }

  return res;
};

export const getTaskConfig = async (token: string) => {
  let error = null;

  const res = await fetch(`${WEBUI_API_BASE_URL}/tasks/config`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    }
  })
    .then(async (res) => {
      if (!res.ok) throw await res.json();
      return res.json();
    })
    .catch((err) => {
      console.log(err);
      error = err;
      return null;
    });

  if (error) {
    throw error;
  }

  return res;
};

export const updateTaskConfig = async (token: string, config: object) => {
  let error = null;

  const res = await fetch(`${WEBUI_API_BASE_URL}/tasks/config/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    },
    body: JSON.stringify(config)
  })
    .then(async (res) => {
      if (!res.ok) throw await res.json();
      return res.json();
    })
    .catch((err) => {
      console.log(err);
      if ('detail' in err) {
        error = err.detail;
      } else {
        error = err;
      }
      return null;
    });

  if (error) {
    throw error;
  }

  return res;
};