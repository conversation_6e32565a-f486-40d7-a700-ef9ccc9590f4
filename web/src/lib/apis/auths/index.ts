import { WEBUI_API_BASE_URL } from '@/lib/constants';

// Admin Config APIs
export const getAdminConfig = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/config`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      console.warn(`Failed to get admin config: HTTP ${response.status}`);
      // Return fallback config
      return {
        ENABLE_SIGNUP: true,
        ENABLE_LOGIN_FORM: true,
        ENABLE_MESSAGE_RATING: true,
        ENABLE_COMMUNITY_SHARING: false,
        DEFAULT_USER_ROLE: 'pending',
        WEBUI_NAME: 'Open WebUI',
        WEBUI_URL: '',
        DEFAULT_MODELS: '',
        DEFAULT_PROMPT_SUGGESTIONS: []
      };
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get admin config:', error);
    // Return fallback config
    return {
      ENABLE_SIGNUP: true,
      ENABLE_LOGIN_FORM: true,
      ENABLE_MESSAGE_RATING: true,
      ENABLE_COMMUNITY_SHARING: false,
      DEFAULT_USER_ROLE: 'pending',
      WEBUI_NAME: 'Open WebUI',
      WEBUI_URL: '',
      DEFAULT_MODELS: '',
      DEFAULT_PROMPT_SUGGESTIONS: []
    };
  }
};

export const updateAdminConfig = async (token: string, config: any): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/config`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      console.warn(`Failed to update admin config: HTTP ${response.status}`);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update admin config:', error);
    return null;
  }
};