'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/stores';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings,
  Cloud,
  Database as DatabaseIcon,
  BarChart3,
  Wrench,
  FileText,
  Globe,
  Code,
  Palette,
  Volume2,
  Image,
  GitBranch,
  Shield,
  ChevronLeft
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

// Import settings components
import General from '@/components/admin/settings/General';
import Connections from '@/components/admin/settings/Connections';
import Models from '@/components/admin/settings/Models';
import Evaluations from '@/components/admin/settings/Evaluations';
import Tools from '@/components/admin/settings/Tools';
import Documents from '@/components/admin/settings/Documents';
import WebSearch from '@/components/admin/settings/WebSearch';
import CodeExecution from '@/components/admin/settings/CodeExecution';
import Interface from '@/components/admin/settings/Interface';
import Audio from '@/components/admin/settings/Audio';
import Images from '@/components/admin/settings/Images';
import Pipelines from '@/components/admin/settings/Pipelines';
import Database from '@/components/admin/settings/Database';

const VALID_TABS = [
  'general',
  'connections', 
  'models',
  'evaluations',
  'tools',
  'documents',
  'web',
  'code-execution',
  'interface',
  'audio',
  'images',
  'pipelines',
  'db'
] as const;

type TabType = typeof VALID_TABS[number];

const TAB_CONFIG: Record<TabType, {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}> = {
  'general': {
    title: 'General',
    icon: Settings,
    description: 'Basic system configuration and settings'
  },
  'connections': {
    title: 'Connections', 
    icon: Cloud,
    description: 'External service connections and integrations'
  },
  'models': {
    title: 'Models',
    icon: DatabaseIcon,
    description: 'AI model configuration and management'
  },
  'evaluations': {
    title: 'Evaluations',
    icon: BarChart3,
    description: 'Model evaluation and performance metrics'
  },
  'tools': {
    title: 'Tools',
    icon: Wrench,
    description: 'Available tools and function management'
  },
  'documents': {
    title: 'Documents',
    icon: FileText,
    description: 'Document processing and RAG settings'
  },
  'web': {
    title: 'Web Search',
    icon: Globe,
    description: 'Web search functionality and providers'
  },
  'code-execution': {
    title: 'Code Execution',
    icon: Code,
    description: 'Code execution environment settings'
  },
  'interface': {
    title: 'Interface',
    icon: Palette,
    description: 'User interface customization'
  },
  'audio': {
    title: 'Audio',
    icon: Volume2,
    description: 'Text-to-speech and audio settings'
  },
  'images': {
    title: 'Images',
    icon: Image,
    description: 'Image generation and processing'
  },
  'pipelines': {
    title: 'Pipelines',
    icon: GitBranch,
    description: 'Custom pipeline configuration'
  },
  'db': {
    title: 'Database',
    icon: DatabaseIcon,
    description: 'Database management and backup'
  }
};

export default function AdminSettingsTabPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuthStore();

  const tab = params.tab as string;
  const currentTab: TabType = VALID_TABS.includes(tab as TabType) ? tab as TabType : 'general';

  // Redirect if invalid tab
  useEffect(() => {
    if (tab && !VALID_TABS.includes(tab as TabType)) {
      router.replace('/admin/settings/general');
    }
  }, [tab, router]);

  // Check admin permissions
  if (!user || user.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="p-8 text-center">
          <Shield className="w-16 h-16 mx-auto mb-4 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-gray-600 dark:text-gray-400">
            You need admin privileges to access this page.
          </p>
        </Card>
      </div>
    );
  }

  const handleTabChange = (newTab: string) => {
    router.push(`/admin/settings/${newTab}`);
  };

  const renderTabContent = () => {
    switch (currentTab) {
      case 'general':
        return <General saveHandler={async () => {}} />;
      case 'connections':
        return <Connections onSave={() => {}} />;
      case 'models':
        return <Models />;
      case 'evaluations':
        return <Evaluations />;
      case 'tools':
        return <Tools />;
      case 'documents':
        return <Documents onSave={() => {}} />;
      case 'web':
        return <WebSearch saveHandler={async () => {}} />;
      case 'code-execution':
        return <CodeExecution saveHandler={async () => {}} />;
      case 'interface':
        return <Interface onSave={() => {}} />;
      case 'audio':
        return <Audio saveHandler={() => {}} />;
      case 'images':
        return <Images onSave={() => {}} />;
      case 'pipelines':
        return <Pipelines saveHandler={() => {}} />;
      case 'db':
        return <Database saveHandler={() => {}} />;
      default:
        return <General saveHandler={async () => {}} />;
    }
  };

  const config = TAB_CONFIG[currentTab];
  const Icon = config.icon;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Link href="/admin" className="hover:text-gray-700">
              Admin
            </Link>
            <span>/</span>
            <Link href="/admin/settings/general" className="hover:text-gray-700">
              Settings
            </Link>
            <span>/</span>
            <span className="font-medium text-gray-900 dark:text-gray-100">
              {config.title}
            </span>
          </div>
          <div className="flex items-center space-x-3 mt-2">
            <Icon className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold">{config.title}</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {config.description}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <Card className="p-4">
            <nav className="space-y-1">
              {VALID_TABS.map((tabKey) => {
                const tabConfig = TAB_CONFIG[tabKey];
                const TabIcon = tabConfig.icon;
                const isActive = currentTab === tabKey;
                
                return (
                  <button
                    key={tabKey}
                    onClick={() => handleTabChange(tabKey)}
                    className={cn(
                      "w-full flex items-center px-3 py-2 text-left text-sm font-medium rounded-md transition-colors",
                      isActive 
                        ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200" 
                        : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
                    )}
                  >
                    <TabIcon className={cn(
                      "w-4 h-4 mr-3",
                      isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-500"
                    )} />
                    {tabConfig.title}
                  </button>
                );
              })}
            </nav>
          </Card>
        </div>

        {/* Content */}
        <div className="flex-1">
          <Card className="p-6">
            {renderTabContent()}
          </Card>
        </div>
      </div>
    </div>
  );
}